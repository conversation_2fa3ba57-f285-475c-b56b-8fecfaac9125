from pydantic import BaseModel, ConfigDict, Field, model_validator


# 动作输入模型
class SearchGoogleAction(BaseModel):
	query: str


class GoToUrlAction(BaseModel):
	url: str


class ClickElementAction(BaseModel):
	index: int
	xpath: str | None = None


class InputTextAction(BaseModel):
	index: int
	text: str
	xpath: str | None = None


class DoneAction(BaseModel):
	text: str
	success: bool
	files_to_display: list[str] | None = []


class SwitchTabAction(BaseModel):
	page_id: int


class OpenTabAction(BaseModel):
	url: str


class CloseTabAction(BaseModel):
	page_id: int


class ScrollAction(BaseModel):
	amount: int | None = None  # 要滚动的像素数。如果为None，则向下/向上滚动一页


class SendKeysAction(BaseModel):
	keys: str


class ExtractPageContentAction(BaseModel):
	value: str


class NoParamsAction(BaseModel):
	"""
	接受传入数据中的任何内容
	并丢弃它，因此最终解析的模型为空。
	"""

	model_config = ConfigDict(extra='allow')

	@model_validator(mode='before')
	def ignore_all_inputs(cls, values):
		# 无论用户发送什么，都丢弃它并返回空。
		return {}


class Position(BaseModel):
	x: int
	y: int


class DragDropAction(BaseModel):
	# 基于元素的方法
	element_source: str | None = Field(None, description='要拖拽的元素的CSS选择器或XPath')
	element_target: str | None = Field(None, description='要放置到的元素的CSS选择器或XPath')
	element_source_offset: Position | None = Field(
		None, description='源元素内开始拖拽的精确位置（从左上角开始的像素）'
	)
	element_target_offset: Position | None = Field(
		None, description='目标元素内放置的精确位置（从左上角开始的像素）'
	)

	# 基于坐标的方法（如果未提供选择器则使用）
	coord_source_x: int | None = Field(None, description='页面上开始拖拽的绝对X坐标（像素）')
	coord_source_y: int | None = Field(None, description='页面上开始拖拽的绝对Y坐标（像素）')
	coord_target_x: int | None = Field(None, description='页面上放置的绝对X坐标（像素）')
	coord_target_y: int | None = Field(None, description='页面上放置的绝对Y坐标（像素）')

	# 通用选项
	steps: int | None = Field(10, description='更平滑移动的中间点数量（推荐5-20）')
	delay_ms: int | None = Field(5, description='步骤之间的延迟毫秒数（0为最快，10-20更自然）')
