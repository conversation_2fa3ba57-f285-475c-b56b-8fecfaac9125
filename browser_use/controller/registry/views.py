from collections.abc import Callable
from typing import TYPE_CHECKING, Any

from langchain_core.language_models.chat_models import BaseChatModel
from pydantic import BaseModel, ConfigDict

from browser_use.browser import BrowserSession
from browser_use.browser.types import Page
from browser_use.filesystem.file_system import FileSystem

if TYPE_CHECKING:
	pass


class RegisteredAction(BaseModel):
	"""已注册动作的模型"""

	name: str
	description: str
	function: Callable
	param_model: type[BaseModel]

	# 过滤器：提供特定域名或函数来确定动作是否应该在给定页面上可用
	domains: list[str] | None = None  # 例如 ['*.google.com', 'www.bing.com', 'yahoo.*]
	page_filter: Callable[[Page], bool] | None = None

	model_config = ConfigDict(arbitrary_types_allowed=True)

	def prompt_description(self) -> str:
		"""获取动作的提示词描述"""
		skip_keys = ['title']
		s = f'{self.description}: \n'
		s += '{' + str(self.name) + ': '
		s += str(
			{
				k: {sub_k: sub_v for sub_k, sub_v in v.items() if sub_k not in skip_keys}
				for k, v in self.param_model.model_json_schema()['properties'].items()
			}
		)
		s += '}'
		return s


class ActionModel(BaseModel):
	"""动态创建的动作模型的基础模型"""

	# 这将包含所有已注册的动作，例如
	# click_element = param_model = ClickElementParams
	# done = param_model = None
	#
	model_config = ConfigDict(arbitrary_types_allowed=True)

	def get_index(self) -> int | None:
		"""获取动作的索引"""
		# {'clicked_element': {'index':5}}
		params = self.model_dump(exclude_unset=True).values()
		if not params:
			return None
		for param in params:
			if param is not None and 'index' in param:
				return param['index']
		return None

	def set_index(self, index: int):
		"""覆盖动作的索引"""
		# 获取动作名称和参数
		action_data = self.model_dump(exclude_unset=True)
		action_name = next(iter(action_data.keys()))
		action_params = getattr(self, action_name)

		# 直接在模型上更新索引
		if hasattr(action_params, 'index'):
			action_params.index = index


class ActionRegistry(BaseModel):
	"""表示动作注册表的模型"""

	actions: dict[str, RegisteredAction] = {}

	@staticmethod
	def _match_domains(domains: list[str] | None, url: str) -> bool:
		"""
		将域名glob模式列表与URL进行匹配。

		Args:
			domains: 可以包含glob模式（*通配符）的域名模式列表
			url: 要匹配的URL

		Returns:
			如果URL的域名匹配模式则返回True，否则返回False
		"""

		if domains is None or not url:
			return True

		# 使用utils中的集中式URL匹配逻辑
		from browser_use.utils import match_url_with_domain_pattern

		for domain_pattern in domains:
			if match_url_with_domain_pattern(url, domain_pattern):
				return True
		return False

	@staticmethod
	def _match_page_filter(page_filter: Callable[[Page], bool] | None, page: Page) -> bool:
		"""将页面过滤器与页面进行匹配"""
		if page_filter is None:
			return True
		return page_filter(page)

	def get_prompt_description(self, page: Page | None = None) -> str:
		"""获取提示词的所有动作描述

		Args:
			page: 如果提供，使用page_filter和domains按页面过滤动作。

		Returns:
			可用动作的字符串描述。
			- 如果page为None：只返回没有page_filter和domains的动作（用于系统提示词）
			- 如果提供了page：只返回匹配当前页面的过滤动作（排除未过滤的动作）
		"""
		if page is None:
			# 对于系统提示词（未提供页面），只包含没有过滤器的动作
			return '\n'.join(
				action.prompt_description()
				for action in self.actions.values()
				if action.page_filter is None and action.domains is None
			)

		# 只包含当前页面的过滤动作
		filtered_actions = []
		for action in self.actions.values():
			if not (action.domains or action.page_filter):
				# 跳过没有过滤器的动作，它们已经包含在系统提示词中
				continue

			domain_is_allowed = self._match_domains(action.domains, page.url)
			page_is_allowed = self._match_page_filter(action.page_filter, page)

			if domain_is_allowed and page_is_allowed:
				filtered_actions.append(action)

		return '\n'.join(action.prompt_description() for action in filtered_actions)


class SpecialActionParameters(BaseModel):
	"""定义可以注入到动作中的所有特殊参数的模型"""

	model_config = ConfigDict(arbitrary_types_allowed=True)

	# 从Agent(context=...)传递下来的可选用户提供的上下文对象
	# 例如可以包含任何内容，外部数据库连接、文件句柄、队列、运行时配置对象等
	# 您可能希望能够从许多动作中快速访问的内容
	# browser-use代码根本不使用这个，我们只是为了方便将其传递给您的动作
	context: Any | None = None

	# browser-use会话对象，可用于创建新标签页、导航、访问playwright对象等
	browser_session: BrowserSession | None = None

	# 对请求旧模型名称的动作的遗留支持
	browser: BrowserSession | None = None
	browser_context: BrowserSession | None = (
		None  # 额外令人困惑，这实际上不是指playwright BrowserContext，
		# 而是指BrowserUse自己的旧BrowserContext对象的名称，来自<v0.2.0
		# 应该在v0.3.0后弃用然后移除以避免歧义
	)  # 我们不能改变得太快，因为野外许多人的自定义动作期望这个参数

	# 动作可以获取playwright Page，page = await browser_session.get_current_page()的快捷方式
	page: Page | None = None

	# 如果动作请求这些参数名称，则额外注入的配置
	page_extraction_llm: BaseChatModel | None = None
	file_system: FileSystem | None = None
	available_file_paths: list[str] | None = None
	has_sensitive_data: bool = False

	@classmethod
	def get_browser_requiring_params(cls) -> set[str]:
		"""获取需要browser_session的参数名称"""
		return {'browser_session', 'browser', 'browser_context', 'page'}
