import asyncio
import functools
import inspect
import logging
import re
from collections.abc import Callable
from inspect import Parameter, iscoroutinefunction, signature
from types import UnionType
from typing import Any, Generic, Optional, TypeVar, Union, get_args, get_origin

from langchain_core.language_models.chat_models import BaseChatModel
from pydantic import BaseModel, Field, create_model

from browser_use.browser import BrowserSession
from browser_use.browser.types import Page
from browser_use.controller.registry.views import (
	ActionModel,
	ActionRegistry,
	RegisteredAction,
	SpecialActionParameters,
)
from browser_use.filesystem.file_system import FileSystem
from browser_use.telemetry.service import ProductTelemetry
from browser_use.telemetry.views import (
	ControllerRegisteredFunctionsTelemetryEvent,
	RegisteredFunction,
)
from browser_use.utils import match_url_with_domain_pattern, time_execution_async

Context = TypeVar('Context')

logger = logging.getLogger(__name__)


class Registry(Generic[Context]):
	"""用于注册和管理动作的服务"""

	def __init__(self, exclude_actions: list[str] | None = None):
		self.registry = ActionRegistry()
		self.telemetry = ProductTelemetry()
		self.exclude_actions = exclude_actions if exclude_actions is not None else []

	def _get_special_param_types(self) -> dict[str, type | UnionType | None]:
		"""从SpecialActionParameters获取特殊参数的预期类型"""
		# 手动定义预期类型以避免Optional处理的问题。
		# 我们应该尽量将此列表减少到0，为所有动作提供尽可能少的标准化对象
		# 但每个驱动程序应该决定什么是相关的来暴露动作方法，
		# 例如playwright页面、2fa代码获取器、sensitive_data包装器、其他上下文等。
		return {
			'context': None,  # Context is a TypeVar, so we can't validate type
			'browser_session': BrowserSession,
			'browser': BrowserSession,  # legacy name
			'browser_context': BrowserSession,  # legacy name
			'page': Page,
			'page_extraction_llm': BaseChatModel,
			'available_file_paths': list,
			'has_sensitive_data': bool,
			'file_system': FileSystem,
		}

	def _normalize_action_function_signature(
		self,
		func: Callable,
		description: str,
		param_model: type[BaseModel] | None = None,
	) -> tuple[Callable, type[BaseModel]]:
		"""
		规范化动作函数以仅接受kwargs。

		Returns:
			- 接受(*_, params: ParamModel, **special_params)的规范化函数
			- 用于注册的参数模型
		"""
		sig = signature(func)
		parameters = list(sig.parameters.values())
		special_param_types = self._get_special_param_types()
		special_param_names = set(special_param_types.keys())

		# 步骤1：验证原始函数签名中没有**kwargs
		# 如果需要默认值，必须使用专用的param_model: BaseModel
		for param in parameters:
			if param.kind == Parameter.VAR_KEYWORD:
				raise ValueError(
					f"动作'{func.__name__}'有**{param.name}，这是不允许的。"
					f'动作必须只有明确的位置参数。'
				)

		# 步骤2：分离特殊参数和动作参数
		action_params = []
		special_params = []
		param_model_provided = param_model is not None

		for i, param in enumerate(parameters):
			# 检查这是否是类型1模式（第一个参数是BaseModel）
			if i == 0 and param_model_provided and param.name not in special_param_names:
				# 这是类型1模式 - 跳过params参数
				continue

			if param.name in special_param_names:
				# 验证特殊参数类型
				expected_type = special_param_types.get(param.name)
				if param.annotation != Parameter.empty and expected_type is not None:
					# 处理Optional类型 - 规范化双方
					param_type = param.annotation
					origin = get_origin(param_type)
					if origin is Union:
						args = get_args(param_type)
						# 找到非None类型
						param_type = next((arg for arg in args if arg is not type(None)), param_type)

					# 检查类型是否兼容（精确匹配、子类或泛型列表）
					types_compatible = (
						param_type == expected_type
						or (
							inspect.isclass(param_type)
							and inspect.isclass(expected_type)
							and issubclass(param_type, expected_type)
						)
						or
						# 处理list[T] vs list比较
						(expected_type is list and (param_type is list or get_origin(param_type) is list))
					)

					if not types_compatible:
						expected_type_name = getattr(expected_type, '__name__', str(expected_type))
						param_type_name = getattr(param_type, '__name__', str(param_type))
						raise ValueError(
							f"动作'{func.__name__}'参数'{param.name}: {param_type_name}'"
							f"与控制器注入的特殊参数冲突：'{param.name}: {expected_type_name}'"
						)
				special_params.append(param)
			else:
				action_params.append(param)

		# 步骤3：创建或验证参数模型
		if not param_model_provided:
			# 类型2：从动作参数生成参数模型
			if action_params:
				params_dict = {}
				for param in action_params:
					annotation = param.annotation if param.annotation != Parameter.empty else str
					default = ... if param.default == Parameter.empty else param.default
					params_dict[param.name] = (annotation, default)

				param_model = create_model(f'{func.__name__}_Params', __base__=ActionModel, **params_dict)
			else:
				# 没有动作参数，创建空模型
				param_model = create_model(
					f'{func.__name__}_Params',
					__base__=ActionModel,
				)
		assert param_model is not None, f'param_model is None for {func.__name__}'

		# 步骤4：创建规范化包装函数
		@functools.wraps(func)
		async def normalized_wrapper(*args, params: BaseModel | None = None, **kwargs):
			"""只接受kwargs的规范化动作"""
			# 验证没有位置参数
			if args:
				raise TypeError(f'{func.__name__}()不接受位置参数，只允许关键字参数')

			# 为原始函数准备参数
			call_args = []
			call_kwargs = {}

			# 处理类型1模式（第一个参数是参数模型）
			if param_model_provided and parameters and parameters[0].name not in special_param_names:
				if params is None:
					raise ValueError(f"{func.__name__}()缺少必需的'params'参数")
				# 对于类型1，我们将使用params对象作为第一个参数
				pass
			else:
				# 类型2模式 - 需要解包params
				# 如果params为None，尝试从kwargs创建它
				if params is None and action_params:
					# 从kwargs中提取动作参数
					action_kwargs = {}
					for param in action_params:
						if param.name in kwargs:
							action_kwargs[param.name] = kwargs[param.name]
					if action_kwargs:
						# 使用具有正确类型定义的param_model
						params = param_model(**action_kwargs)

			# 通过按顺序迭代原始函数参数来构建call_args
			params_dict = params.model_dump() if params is not None else {}

			for i, param in enumerate(parameters):
				# 对于类型1模式跳过第一个参数（它是模型本身）
				if param_model_provided and i == 0 and param.name not in special_param_names:
					call_args.append(params)
				elif param.name in special_param_names:
					# 这是一个特殊参数
					if param.name in kwargs:
						value = kwargs[param.name]
						# 检查必需的特殊参数是否为None
						if value is None and param.default == Parameter.empty:
							if param.name == 'browser_session':
								raise ValueError(f'动作 {func.__name__} 需要browser_session但未提供。')
							elif param.name == 'page_extraction_llm':
								raise ValueError(f'动作 {func.__name__} 需要page_extraction_llm但未提供。')
							elif param.name == 'file_system':
								raise ValueError(f'动作 {func.__name__} 需要file_system但未提供。')
							elif param.name == 'page':
								raise ValueError(f'动作 {func.__name__} 需要page但未提供。')
							else:
								raise ValueError(f"{func.__name__}()缺少必需的特殊参数'{param.name}'")
						call_args.append(value)
					elif param.default != Parameter.empty:
						call_args.append(param.default)
					else:
						# 特殊参数是必需的但未提供
						if param.name == 'browser_session':
							raise ValueError(f'动作 {func.__name__} 需要browser_session但未提供。')
						elif param.name == 'page_extraction_llm':
							raise ValueError(f'动作 {func.__name__} 需要page_extraction_llm但未提供。')
						elif param.name == 'file_system':
							raise ValueError(f'动作 {func.__name__} 需要file_system但未提供。')
						elif param.name == 'page':
							raise ValueError(f'动作 {func.__name__} 需要page但未提供。')
						else:
							raise ValueError(f"{func.__name__}()缺少必需的特殊参数'{param.name}'")
				else:
					# 这是一个动作参数
					if param.name in params_dict:
						call_args.append(params_dict[param.name])
					elif param.default != Parameter.empty:
						call_args.append(param.default)
					else:
						raise ValueError(f"{func.__name__}()缺少必需的参数'{param.name}'")

			# 使用位置参数调用原始函数
			if iscoroutinefunction(func):
				return await func(*call_args)
			else:
				return await asyncio.to_thread(func, *call_args)

		# 更新包装器签名为仅关键字参数
		new_params = [Parameter('params', Parameter.KEYWORD_ONLY, default=None, annotation=Optional[param_model])]

		# 将特殊参数添加为仅关键字参数
		for sp in special_params:
			new_params.append(Parameter(sp.name, Parameter.KEYWORD_ONLY, default=sp.default, annotation=sp.annotation))

		# 添加**kwargs以接受和忽略额外参数
		new_params.append(Parameter('kwargs', Parameter.VAR_KEYWORD))

		normalized_wrapper.__signature__ = sig.replace(parameters=new_params)  # type: ignore[attr-defined]

		return normalized_wrapper, param_model

	# @time_execution_sync('--create_param_model')
	def _create_param_model(self, function: Callable) -> type[BaseModel]:
		"""从函数签名创建Pydantic模型"""
		sig = signature(function)
		special_param_names = set(SpecialActionParameters.model_fields.keys())
		params = {
			name: (param.annotation, ... if param.default == param.empty else param.default)
			for name, param in sig.parameters.items()
			if name not in special_param_names
		}
		# TODO: 使这里的类型工作
		return create_model(
			f'{function.__name__}_parameters',
			__base__=ActionModel,
			**params,  # type: ignore
		)

	def action(
		self,
		description: str,
		param_model: type[BaseModel] | None = None,
		domains: list[str] | None = None,
		allowed_domains: list[str] | None = None,
		page_filter: Callable[[Any], bool] | None = None,
	):
		"""用于注册动作的装饰器"""
		# 处理别名：domains和allowed_domains是同一个参数
		if allowed_domains is not None and domains is not None:
			raise ValueError("不能同时指定'domains'和'allowed_domains' - 它们是同一个参数的别名")

		final_domains = allowed_domains if allowed_domains is not None else domains

		def decorator(func: Callable):
			# 如果动作在exclude_actions中则跳过注册
			if func.__name__ in self.exclude_actions:
				return func

			# 规范化函数签名
			normalized_func, actual_param_model = self._normalize_action_function_signature(func, description, param_model)

			action = RegisteredAction(
				name=func.__name__,
				description=description,
				function=normalized_func,
				param_model=actual_param_model,
				domains=final_domains,
				page_filter=page_filter,
			)
			self.registry.actions[func.__name__] = action

			# 返回规范化函数，以便可以使用kwargs调用
			return normalized_func

		return decorator

	@time_execution_async('--execute_action')
	async def execute_action(
		self,
		action_name: str,
		params: dict,
		browser_session: BrowserSession | None = None,
		page_extraction_llm: BaseChatModel | None = None,
		file_system: FileSystem | None = None,
		sensitive_data: dict[str, str | dict[str, str]] | None = None,
		available_file_paths: list[str] | None = None,
		#
		context: Context | None = None,
	) -> Any:
		"""使用简化的参数处理执行已注册的动作"""
		if action_name not in self.registry.actions:
			raise ValueError(f'动作 {action_name} 未找到')

		action = self.registry.actions[action_name]
		try:
			# 创建验证的Pydantic模型
			try:
				validated_params = action.param_model(**params)
			except Exception as e:
				raise ValueError(f'动作 {action_name} 的参数 {params} 无效: {type(e)}: {e}') from e

			if sensitive_data:
				# 如果提供了browser_session则获取当前URL
				current_url = None
				if browser_session:
					if browser_session.agent_current_page:
						current_url = browser_session.agent_current_page.url
					else:
						current_page = await browser_session.get_current_page()
						current_url = current_page.url if current_page else None
				validated_params = self._replace_sensitive_data(validated_params, sensitive_data, current_url)

			# 构建特殊上下文字典
			special_context = {
				'context': context,
				'browser_session': browser_session,
				'browser': browser_session,  # 遗留支持
				'browser_context': browser_session,  # 遗留支持
				'page_extraction_llm': page_extraction_llm,
				'available_file_paths': available_file_paths,
				'has_sensitive_data': action_name == 'input_text' and bool(sensitive_data),
				'file_system': file_system,
			}

			# 如果需要，处理异步页面参数
			if browser_session:
				# 检查函数签名是否包含'page'参数
				sig = signature(action.function)
				if 'page' in sig.parameters:
					special_context['page'] = await browser_session.get_current_page()

			# 所有函数现在都规范化为仅接受kwargs
			# 使用params和解包的特殊上下文调用
			try:
				return await action.function(params=validated_params, **special_context)
			except Exception as e:
				# 如果是页面错误则重试一次
				logger.warning(f'⚠️ 动作 {action_name}() 失败: {type(e).__name__}: {e}，再尝试一次...')
				special_context['page'] = browser_session and await browser_session.get_current_page()
				try:
					return await action.function(params=validated_params, **special_context)
				except Exception as retry_error:
					raise RuntimeError(
						f'动作 {action_name}() 失败: {type(e).__name__}: {e} (页面可能在动作执行过程中关闭或导航离开)'
					) from retry_error
				raise

		except ValueError as e:
			# 保留验证的ValueError消息
			if 'requires browser_session but none provided' in str(e) or 'requires page_extraction_llm but none provided' in str(
				e
			):
				raise RuntimeError(str(e)) from e
			else:
				raise RuntimeError(f'执行动作 {action_name} 时出错: {str(e)}') from e
		except Exception as e:
			raise RuntimeError(f'执行动作 {action_name} 时出错: {str(e)}') from e

	def _log_sensitive_data_usage(self, placeholders_used: set[str], current_url: str | None) -> None:
		"""记录在页面上使用敏感数据的情况"""
		if placeholders_used:
			url_info = f' 在 {current_url}' if current_url and current_url != 'about:blank' else ''
			logger.info(f'🔒 使用敏感数据占位符: {", ".join(sorted(placeholders_used))}{url_info}')

	def _replace_sensitive_data(
		self, params: BaseModel, sensitive_data: dict[str, Any], current_url: str | None = None
	) -> BaseModel:
		"""
		用实际值替换params中的敏感数据占位符。

		Args:
			params: 包含<secret>placeholder</secret>标签的参数对象
			sensitive_data: 敏感数据字典，可以是旧格式{key: value}
						   或新格式{domain_pattern: {key: value}}
			current_url: 用于域名匹配的可选当前URL

		Returns:
			BaseModel: 占位符被实际值替换的参数对象
		"""
		secret_pattern = re.compile(r'<secret>(.*?)</secret>')

		# 用于跟踪整个对象中所有缺失占位符的集合
		all_missing_placeholders = set()
		# 用于跟踪成功替换的占位符的集合
		replaced_placeholders = set()

		# 根据格式和当前URL处理敏感数据
		applicable_secrets = {}

		for domain_or_key, content in sensitive_data.items():
			if isinstance(content, dict):
				# 新格式：{domain_pattern: {key: value}}
				# 只包含匹配当前URL的域名的秘密
				if current_url and current_url != 'about:blank':
					# 这是一个真实的url，使用我们自定义的allowed_domains scheme://*.example.com glob匹配检查
					if match_url_with_domain_pattern(current_url, domain_or_key):
						applicable_secrets.update(content)
			else:
				# 旧格式：{key: value}，暴露给所有域名（仅出于遗留原因允许）
				applicable_secrets[domain_or_key] = content

		# 过滤掉空值
		applicable_secrets = {k: v for k, v in applicable_secrets.items() if v}

		def recursively_replace_secrets(value: str | dict | list) -> str | dict | list:
			if isinstance(value, str):
				matches = secret_pattern.findall(value)

				for placeholder in matches:
					if placeholder in applicable_secrets:
						value = value.replace(f'<secret>{placeholder}</secret>', applicable_secrets[placeholder])
						replaced_placeholders.add(placeholder)
					else:
						# 跟踪缺失的占位符
						all_missing_placeholders.add(placeholder)
						# 不替换标签，保持原样

				return value
			elif isinstance(value, dict):
				return {k: recursively_replace_secrets(v) for k, v in value.items()}
			elif isinstance(value, list):
				return [recursively_replace_secrets(v) for v in value]
			return value

		params_dump = params.model_dump()
		processed_params = recursively_replace_secrets(params_dump)

		# 记录敏感数据使用情况
		self._log_sensitive_data_usage(replaced_placeholders, current_url)

		# 如果有任何占位符缺失则记录警告
		if all_missing_placeholders:
			logger.warning(f'sensitive_data字典中缺失或为空的键: {", ".join(all_missing_placeholders)}')

		return type(params).model_validate(processed_params)

	# @time_execution_sync('--create_action_model')
	def create_action_model(self, include_actions: list[str] | None = None, page=None) -> type[ActionModel]:
		"""从已注册的动作创建Pydantic模型，供支持工具调用和强制模式的LLM API使用"""

		# 如果提供了页面则基于页面过滤动作：
		#   如果page为None，只包含没有过滤器的动作
		#   如果提供了page，只包含匹配页面的动作

		available_actions = {}
		for name, action in self.registry.actions.items():
			if include_actions is not None and name not in include_actions:
				continue

			# If no page provided, only include actions with no filters
			if page is None:
				if action.page_filter is None and action.domains is None:
					available_actions[name] = action
				continue

			# Check page_filter if present
			domain_is_allowed = self.registry._match_domains(action.domains, page.url)
			page_is_allowed = self.registry._match_page_filter(action.page_filter, page)

			# Include action if both filters match (or if either is not present)
			if domain_is_allowed and page_is_allowed:
				available_actions[name] = action

		fields = {
			name: (
				Optional[action.param_model],
				Field(default=None, description=action.description),
			)
			for name, action in available_actions.items()
		}

		self.telemetry.capture(
			ControllerRegisteredFunctionsTelemetryEvent(
				registered_functions=[
					RegisteredFunction(name=name, params=action.param_model.model_json_schema())
					for name, action in available_actions.items()
				]
			)
		)

		return create_model('ActionModel', __base__=ActionModel, **fields)  # type:ignore

	def get_prompt_description(self, page=None) -> str:
		"""Get a description of all actions for the prompt

		If page is provided, only include actions that are available for that page
		based on their filter_func
		"""
		return self.registry.get_prompt_description(page=page)
