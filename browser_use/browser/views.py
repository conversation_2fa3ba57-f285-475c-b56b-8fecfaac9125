from dataclasses import dataclass, field
from typing import Any

from pydantic import BaseModel

from browser_use.dom.history_tree_processor.service import DOMHistoryElement
from browser_use.dom.views import DOMState


# Pydantic
class TabInfo(BaseModel):
	"""表示浏览器标签页信息"""

	page_id: int
	url: str
	title: str
	parent_page_id: int | None = None  # 包含此弹窗或跨域iframe的父页面


@dataclass
class BrowserStateSummary(DOMState):
	"""为LLM处理而设计的浏览器当前状态摘要"""

	# 由DOMState提供：
	# element_tree: DOMElementNode
	# selector_map: SelectorMap

	url: str
	title: str
	tabs: list[TabInfo]
	screenshot: str | None = field(default=None, repr=False)
	pixels_above: int = 0
	pixels_below: int = 0
	browser_errors: list[str] = field(default_factory=list)


@dataclass
class BrowserStateHistory:
	"""在过去某个时间点的浏览器状态摘要，用于LLM消息历史"""

	url: str
	title: str
	tabs: list[TabInfo]
	interacted_element: list[DOMHistoryElement | None] | list[None]
	screenshot: str | None = None

	def to_dict(self) -> dict[str, Any]:
		data = {}
		data['tabs'] = [tab.model_dump() for tab in self.tabs]
		data['screenshot'] = self.screenshot
		data['interacted_element'] = [el.to_dict() if el else None for el in self.interacted_element]
		data['url'] = self.url
		data['title'] = self.title
		return data


class BrowserError(Exception):
	"""所有浏览器错误的基类"""


class URLNotAllowedError(BrowserError):
	"""当URL不被允许时抛出的错误"""
