# 代码库结构

> 代码结构灵感来源于 https://github.com/Netflix/dispatch。

关于如何构建可扩展代码库的优秀结构也可以在[这个仓库](https://github.com/zhanymkanov/fastapi-best-practices)中找到。

这是一个关于我们应该如何构建后端代码库的简要文档。

## 代码结构

```markdown
src/
/<服务名称>/
models.py
services.py
prompts.py
views.py
utils.py
routers.py

    	/_<子服务名称>/
```

### Service.py

始终是单个文件，除非变得太长 - 超过约500行，则拆分为\_子服务

### Views.py

始终将视图拆分为两部分

```python
# 全部
...

# 请求
...

# 响应
...
```

如果太长 → 拆分为多个文件

### Prompts.py

单个文件；如果太长 → 拆分为多个文件（每个文件一个提示词等）

### Routers.py

永远不要拆分为多个文件
