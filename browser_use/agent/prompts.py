import importlib.resources
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from langchain_core.messages import HumanMessage, SystemMessage

if TYPE_CHECKING:
	from browser_use.agent.views import AgentStepInfo
	from browser_use.browser.views import BrowserStateSummary
	from browser_use.filesystem.file_system import FileSystem


class SystemPrompt:
	def __init__(
		self,
		action_description: str,
		max_actions_per_step: int = 10,
		override_system_message: str | None = None,
		extend_system_message: str | None = None,
	):
		self.default_action_description = action_description
		self.max_actions_per_step = max_actions_per_step
		prompt = ''
		if override_system_message:
			prompt = override_system_message
		else:
			self._load_prompt_template()
			prompt = self.prompt_template.format(max_actions=self.max_actions_per_step)

		if extend_system_message:
			prompt += f'\n{extend_system_message}'

		self.system_message = SystemMessage(content=prompt)

	def _load_prompt_template(self) -> None:
		"""从markdown文件加载提示词模板。"""
		try:
			# 这在开发环境和作为包安装时都有效
			with importlib.resources.files('browser_use.agent').joinpath('system_prompt.md').open('r', encoding='utf-8') as f:
				self.prompt_template = f.read()
		except Exception as e:
			raise RuntimeError(f'加载系统提示词模板失败: {e}')

	def get_system_message(self) -> SystemMessage:
		"""
		获取agent的系统提示词。

		Returns:
		    SystemMessage: 格式化的系统提示词
		"""
		return self.system_message


# 功能：
# {self.default_action_description}

# 示例：
# {self.example_response()}
# 您的可用动作：
# {self.default_action_description}


class AgentMessagePrompt:
	def __init__(
		self,
		browser_state_summary: 'BrowserStateSummary',
		file_system: 'FileSystem',
		agent_history_description: str | None = None,
		read_state_description: str | None = None,
		task: str | None = None,
		include_attributes: list[str] | None = None,
		step_info: Optional['AgentStepInfo'] = None,
		page_filtered_actions: str | None = None,
		max_clickable_elements_length: int = 40000,
		sensitive_data: str | None = None,
	):
		self.browser_state: 'BrowserStateSummary' = browser_state_summary
		self.file_system: 'FileSystem | None' = file_system
		self.agent_history_description: str | None = agent_history_description
		self.read_state_description: str | None = read_state_description
		self.task: str | None = task
		self.include_attributes = include_attributes or []
		self.step_info = step_info
		self.page_filtered_actions: str | None = page_filtered_actions
		self.max_clickable_elements_length: int = max_clickable_elements_length
		self.sensitive_data: str | None = sensitive_data
		assert self.browser_state

	def _get_browser_state_description(self) -> str:
		elements_text = self.browser_state.element_tree.clickable_elements_to_string(include_attributes=self.include_attributes)

		if len(elements_text) > self.max_clickable_elements_length:
			elements_text = elements_text[: self.max_clickable_elements_length]
			truncated_text = f' (truncated to {self.max_clickable_elements_length} characters)'
		else:
			truncated_text = ''

		has_content_above = (self.browser_state.pixels_above or 0) > 0
		has_content_below = (self.browser_state.pixels_below or 0) > 0

		if elements_text != '':
			if has_content_above:
				elements_text = f'... {self.browser_state.pixels_above} pixels above - scroll to see more or extract structured data if you are looking for specific information ...\n{elements_text}'
			else:
				elements_text = f'[Start of page]\n{elements_text}'
			if has_content_below:
				elements_text = f'{elements_text}\n... {self.browser_state.pixels_below} pixels below - scroll to see more or extract structured data if you are looking for specific information ...'
			else:
				elements_text = f'{elements_text}\n[End of page]'
		else:
			elements_text = 'empty page'

		tabs_text = ''
		current_tab_candidates = []

		# 查找同时匹配URL和标题的标签页，以更可靠地识别当前标签页
		for tab in self.browser_state.tabs:
			if tab.url == self.browser_state.url and tab.title == self.browser_state.title:
				current_tab_candidates.append(tab.page_id)

		# 如果我们有确切的一个匹配，将其标记为当前标签页
		# 否则，不标记任何标签页为当前标签页以避免混淆
		current_tab_id = current_tab_candidates[0] if len(current_tab_candidates) == 1 else None

		for tab in self.browser_state.tabs:
			tabs_text += f'Tab {tab.page_id}: {tab.url} - {tab.title[:30]}\n'

		current_tab_text = f'Current tab: {current_tab_id}' if current_tab_id is not None else ''

		browser_state = f"""{current_tab_text}
Available tabs:
{tabs_text}
Interactive elements from top layer of the current page inside the viewport{truncated_text}:
{elements_text}
"""
		return browser_state

	def _get_agent_state_description(self) -> str:
		if self.step_info:
			step_info_description = f'第 {self.step_info.step_number + 1} 步，最多 {self.step_info.max_steps} 步\n'
		else:
			step_info_description = ''
		time_str = datetime.now().strftime('%Y-%m-%d %H:%M')
		step_info_description += f'当前日期和时间: {time_str}'

		todo_contents = self.file_system.get_todo_contents() if self.file_system else ''
		if not len(todo_contents):
			todo_contents = '[当前todo.md为空，适当时请填写您的计划]'

		agent_state = f"""
<user_request>
{self.task}
</user_request>
<file_system>
{self.file_system.describe() if self.file_system else '无可用文件系统'}
</file_system>
<todo_contents>
{todo_contents}
</todo_contents>
"""
		if self.sensitive_data:
			agent_state += f'<sensitive_data>\n{self.sensitive_data}\n</sensitive_data>\n'

		agent_state += f'<step_info>\n{step_info_description}\n</step_info>\n'
		return agent_state

	def get_user_message(self, use_vision: bool = True) -> HumanMessage:
		state_description = (
			'<agent_history>\n'
			+ (self.agent_history_description.strip('\n') if self.agent_history_description else '')
			+ '\n</agent_history>\n'
		)
		state_description += '<agent_state>\n' + self._get_agent_state_description().strip('\n') + '\n</agent_state>\n'
		state_description += '<browser_state>\n' + self._get_browser_state_description().strip('\n') + '\n</browser_state>\n'
		state_description += (
			'<read_state>\n'
			+ (self.read_state_description.strip('\n') if self.read_state_description else '')
			+ '\n</read_state>\n'
		)
		if self.page_filtered_actions:
			state_description += '对于此页面，这些额外的动作可用:\n'
			state_description += self.page_filtered_actions + '\n'

		if self.browser_state.screenshot and use_vision is True:
			# 为视觉模型格式化消息
			return HumanMessage(
				content=[
					{'type': 'text', 'text': state_description},
					{
						'type': 'image_url',
						'image_url': {'url': f'data:image/png;base64,{self.browser_state.screenshot}'},  # , 'detail': 'low'
					},
				]
			)

		return HumanMessage(content=state_description)


class PlannerPrompt(SystemPrompt):
	def __init__(self, available_actions: str):
		self.available_actions = available_actions

	def get_system_message(
		self, is_planner_reasoning: bool, extended_planner_system_prompt: str | None = None
	) -> SystemMessage | HumanMessage:
		"""获取规划器的系统消息。

		Args:
		    is_planner_reasoning: 如果为True，返回HumanMessage用于思维链
		    extended_planner_system_prompt: 可选的文本，附加到基础提示词

		Returns:
		    根据is_planner_reasoning返回SystemMessage或HumanMessage
		"""

		planner_prompt_text = """
你是一个规划代理，帮助将任务分解为更小的步骤并对当前状态进行推理。
你的角色是：
1. 分析当前状态和历史
2. 评估朝向最终目标的进展
3. 识别潜在的挑战或障碍
4. 建议下一步要采取的高级步骤

在你的消息中，会有来自不同代理的AI消息，格式不同。

你的输出格式应该始终是一个JSON对象，包含以下字段：
{{
    "state_analysis": "对当前状态和到目前为止所做工作的简要分析",
    "progress_evaluation": "对朝向最终目标进展的评估（百分比和描述）",
    "challenges": "列出任何潜在的挑战或障碍",
    "next_steps": "列出2-3个具体的下一步",
    "reasoning": "解释你建议这些下一步的推理"
}}

忽略其他AI消息的输出结构。

保持你的回应简洁并专注于可操作的见解。
"""

		if extended_planner_system_prompt:
			planner_prompt_text += f'\n{extended_planner_system_prompt}'

		if is_planner_reasoning:
			return HumanMessage(content=planner_prompt_text)
		else:
			return SystemMessage(content=planner_prompt_text)
