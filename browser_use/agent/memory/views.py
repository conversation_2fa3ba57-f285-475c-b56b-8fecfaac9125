from typing import Any, Literal

from langchain_core.language_models.chat_models import BaseChatModel
from pydantic import BaseModel, ConfigDict, Field


class MemoryConfig(BaseModel):
	"""程序性记忆的配置。"""

	model_config = ConfigDict(
		from_attributes=True, validate_default=True, revalidate_instances='always', validate_assignment=True
	)

	# 记忆设置
	agent_id: str = Field(default='browser_use_agent', min_length=1)
	memory_interval: int = Field(default=10, gt=1, lt=100)

	# 嵌入器(Embedder)设置
	embedder_provider: Literal['openai', 'gemini', 'ollama', 'huggingface'] = 'huggingface'
	embedder_model: str = Field(min_length=2, default='all-MiniLM-L6-v2')
	embedder_dims: int = Field(default=384, gt=10, lt=10000)

	# LLM设置 - LLM实例可以单独传递
	llm_provider: Literal['langchain'] = 'langchain'
	llm_instance: BaseChatModel | None = None

	# 向量存储设置
	vector_store_provider: Literal[
		'faiss',
		'qdrant',
		'pinecone',
		'supabase',
		'elasticsearch',
		'chroma',
		'weaviate',
		'milvus',
		'pgvector',
		'upstash_vector',
		'vertex_ai_vector_search',
		'azure_ai_search',
		'redis',
	] = Field(default='faiss', description='与Mem0一起使用的向量存储提供商。')

	vector_store_collection_name: str | None = Field(
		default=None,
		description='可选：向量存储中集合/索引的名称。如果为None，将为本地存储生成默认值或由Mem0使用。',
	)

	vector_store_base_path: str = Field(
		default='/tmp/mem0',
		description='本地向量存储（如FAISS、Chroma或Qdrant基于文件）的基础路径，如果在覆盖中没有提供特定路径。',
	)

	vector_store_config_override: dict[str, Any] | None = Field(
		default=None,
		description="高级：覆盖或提供Mem0期望的所选vector_store提供商的'config'字典的额外配置键（例如host、port、api_key）。",
	)

	@property
	def vector_store_path(self) -> str:
		"""返回当前配置的完整向量存储路径。例如 /tmp/mem0_384_faiss"""
		return f'{self.vector_store_base_path}_{self.embedder_dims}_{self.vector_store_provider}'

	@property
	def embedder_config_dict(self) -> dict[str, Any]:
		"""返回嵌入器配置字典。"""
		return {
			'provider': self.embedder_provider,
			'config': {'model': self.embedder_model, 'embedding_dims': self.embedder_dims},
		}

	@property
	def llm_config_dict(self) -> dict[str, Any]:
		"""返回LLM配置字典。"""
		return {'provider': self.llm_provider, 'config': {'model': self.llm_instance}}

	@property
	def vector_store_config_dict(self) -> dict[str, Any]:
		"""
		返回为Mem0定制的向量存储配置字典，
		针对所选提供商进行调整。
		"""
		provider_specific_config: dict[str, Any] = {'embedding_model_dims': self.embedder_dims}

		# --- 默认collection_name处理 ---
		if self.vector_store_collection_name:
			provider_specific_config['collection_name'] = self.vector_store_collection_name
		else:
			is_local_file_storage_mode = False
			is_qdrant_server_mode = False

			if self.vector_store_provider == 'faiss':
				is_local_file_storage_mode = True
			elif self.vector_store_provider == 'chroma':
				# 如果没有配置host/port覆盖，Chroma是本地文件模式
				if not (
					self.vector_store_config_override
					and ('host' in self.vector_store_config_override or 'port' in self.vector_store_config_override)
				):
					is_local_file_storage_mode = True
			elif self.vector_store_provider == 'qdrant':
				has_path_override = self.vector_store_config_override and 'path' in self.vector_store_config_override
				is_server_configured = self.vector_store_config_override and (
					'host' in self.vector_store_config_override
					or 'port' in self.vector_store_config_override
					or 'url' in self.vector_store_config_override
					or 'api_key' in self.vector_store_config_override
				)
				if has_path_override or not is_server_configured:
					is_local_file_storage_mode = True
				if is_server_configured:  # 即使设置了路径，对于某些混合qdrant设置也可以是服务器模式
					is_qdrant_server_mode = True

			if is_local_file_storage_mode:
				provider_specific_config['collection_name'] = f'mem0_{self.vector_store_provider}_{self.embedder_dims}'
			elif self.vector_store_provider == 'upstash_vector':
				provider_specific_config['collection_name'] = ''
			elif (
				self.vector_store_provider
				in ['elasticsearch', 'milvus', 'pgvector', 'redis', 'weaviate', 'supabase', 'azure_ai_search']
				or (self.vector_store_provider == 'qdrant' and is_qdrant_server_mode and not is_local_file_storage_mode)
				or (self.vector_store_provider == 'qdrant' and not is_local_file_storage_mode)
			):  # Qdrant in explicit server mode
				provider_specific_config['collection_name'] = 'mem0'
			else:
				# 对于Pinecone、VertexAI等提供商的后备方案（通常需要用户提供名称）
				# 或者如果添加了新提供商但尚未明确处理。
				provider_specific_config['collection_name'] = 'mem0_default_collection'

		# --- 本地基于文件的存储的默认路径处理 ---
		default_local_path = f'{self.vector_store_base_path}_{self.embedder_dims}_{self.vector_store_provider}'

		if self.vector_store_provider == 'faiss':
			if not (self.vector_store_config_override and 'path' in self.vector_store_config_override):
				provider_specific_config['path'] = default_local_path

		elif self.vector_store_provider == 'chroma':
			# 如果Chroma处于本地模式且路径未被覆盖，则设置默认路径
			is_chroma_server_mode = self.vector_store_config_override and (
				'host' in self.vector_store_config_override or 'port' in self.vector_store_config_override
			)
			path_in_override = self.vector_store_config_override and 'path' in self.vector_store_config_override

			if not is_chroma_server_mode and not path_in_override:
				provider_specific_config['path'] = default_local_path

		elif self.vector_store_provider == 'qdrant':
			# 如果Qdrant处于本地文件模式且路径未被覆盖，则设置默认路径
			has_path_override = self.vector_store_config_override and 'path' in self.vector_store_config_override
			is_server_configured = self.vector_store_config_override and (
				'host' in self.vector_store_config_override
				or 'port' in self.vector_store_config_override
				or 'url' in self.vector_store_config_override
				or 'api_key' in self.vector_store_config_override
			)

			if not has_path_override and not is_server_configured:
				provider_specific_config['path'] = default_local_path

		# 合并用户提供的覆盖。这些可以添加新键或覆盖上面设置的默认值。
		if self.vector_store_config_override:
			provider_specific_config.update(self.vector_store_config_override)

		return {
			'provider': self.vector_store_provider,
			'config': provider_specific_config,
		}

	@property
	def full_config_dict(self) -> dict[str, Any]:
		"""返回Mem0的完整配置字典。"""
		return {
			'embedder': self.embedder_config_dict,
			'llm': self.llm_config_dict,
			'vector_store': self.vector_store_config_dict,
		}
