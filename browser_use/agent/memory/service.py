from __future__ import annotations

import logging
import os
import warnings
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON><PERSON>cutor, TimeoutError

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import (
	BaseMessage,
	HumanMessage,
)
from langchain_core.messages.utils import convert_to_openai_messages

from browser_use.agent.memory.views import MemoryConfig
from browser_use.agent.message_manager.service import MessageManager
from browser_use.agent.message_manager.views import ManagedMessage, MessageMetadata
from browser_use.config import CONFIG
from browser_use.utils import time_execution_sync


class Memory:
	"""
	管理代理的程序性记忆(Procedural Memory)。

	这个类使用Mem0实现了一个程序性记忆管理系统，将代理交互历史在指定间隔内转换为
	简洁、结构化的表示。它通过将冗长的历史信息转换为紧凑但全面的记忆构造来优化
	上下文窗口的利用，同时保留基本的操作知识。
	"""

	def __init__(
		self,
		message_manager: MessageManager,
		llm: BaseChatModel,
		config: MemoryConfig | None = None,
		logger: logging.Logger | None = None,
	):
		self.message_manager = message_manager
		self.llm = llm
		self.logger = logger or logging.getLogger(__name__)

		# 如果未提供配置，则基于LLM初始化默认配置
		if config is None:
			self.config = MemoryConfig(llm_instance=llm, agent_id=f'agent_{id(self)}')

			# 根据LLM类型设置适当的嵌入器(embedder)
			llm_class = llm.__class__.__name__
			if llm_class == 'ChatOpenAI':
				self.config.embedder_provider = 'openai'
				self.config.embedder_model = 'text-embedding-3-small'
				self.config.embedder_dims = 1536
			elif llm_class == 'ChatGoogleGenerativeAI':
				self.config.embedder_provider = 'gemini'
				self.config.embedder_model = 'models/text-embedding-004'
				self.config.embedder_dims = 768
			elif llm_class == 'ChatOllama':
				self.config.embedder_provider = 'ollama'
				self.config.embedder_model = 'nomic-embed-text'
				self.config.embedder_dims = 512
		else:
			# 确保在配置中设置了LLM实例
			self.config = MemoryConfig.model_validate(config)  # 使用Pydantic重新验证
			self.config.llm_instance = llm

		# 检查必需的包
		try:
			# 当ANONYMIZED_TELEMETRY=False时也禁用mem0的遥测
			if not CONFIG.ANONYMIZED_TELEMETRY:
				os.environ['MEM0_TELEMETRY'] = 'False'
			from mem0 import Memory as Mem0Memory
		except ImportError:
			raise ImportError('当enable_memory=True时需要mem0。请使用`pip install mem0`安装。')

		if self.config.embedder_provider == 'huggingface':
			try:
				# 如果使用huggingface，检查是否安装了必需的包
				from sentence_transformers import SentenceTransformer  # noqa: F401 # type: ignore
			except ImportError:
				raise ImportError(
					'当enable_memory=True且embedder_provider="huggingface"时需要sentence_transformers。请使用`pip install sentence-transformers`安装。'
				)

		# 使用配置初始化Mem0
		with warnings.catch_warnings():
			warnings.filterwarnings('ignore', category=DeprecationWarning)
			try:
				self.mem0 = Mem0Memory.from_config(config_dict=self.config.full_config_dict)
			except Exception as e:
				if 'history_old' in str(e) and 'sqlite3.OperationalError' in str(type(e)):
					# 通过使用唯一的历史数据库路径来处理迁移错误
					import tempfile
					import uuid

					self.logger.warning(
						f'⚠️ 在{self.config.full_config_dict}中检测到Mem0 SQLite迁移错误。使用临时数据库以避免冲突。\n{type(e).__name__}: {e}'
					)
					# 创建唯一的临时数据库路径
					temp_dir = tempfile.gettempdir()
					unique_id = str(uuid.uuid4())[:8]
					history_db_path = os.path.join(temp_dir, f'browser_use_mem0_history_{unique_id}.db')

					# 将history_db_path添加到配置中
					config_with_history_path = self.config.full_config_dict.copy()
					config_with_history_path['history_db_path'] = history_db_path

					# 使用新配置重试
					self.mem0 = Mem0Memory.from_config(config_dict=config_with_history_path)
				else:
					# 如果是不同的错误则重新抛出
					raise

	@time_execution_sync('--create_procedural_memory')
	def create_procedural_memory(self, current_step: int) -> None:
		"""
		根据当前步骤在需要时创建程序性记忆。

		Args:
		    current_step: 代理的当前步骤编号
		"""
		self.logger.debug(f'📜 在步骤{current_step}创建程序性记忆')

		# 获取所有消息
		all_messages = self.message_manager.state.history.messages

		# 将消息分为保持原样的消息和需要处理为记忆的消息
		new_messages = []
		messages_to_process = []

		for msg in all_messages:
			if isinstance(msg, ManagedMessage) and msg.metadata.message_type in {'init', 'memory'}:
				# 保持系统和记忆消息原样
				new_messages.append(msg)
			else:
				if len(msg.message.content) > 0:
					messages_to_process.append(msg)

		# 需要至少2条消息才能创建有意义的摘要
		if len(messages_to_process) <= 1:
			self.logger.debug('📜 没有足够的非记忆消息来总结')
			return
		# 创建带超时的程序性记忆
		try:
			with ThreadPoolExecutor(max_workers=1) as executor:
				future = executor.submit(self._create, [m.message for m in messages_to_process], current_step)
				memory_content = future.result(timeout=5)
		except TimeoutError:
			self.logger.warning('📜 程序性记忆创建在30秒后超时')
			return
		except Exception as e:
			self.logger.error(f'📜 程序性记忆创建过程中出错: {e}')
			return

		if not memory_content:
			self.logger.warning('📜 创建程序性记忆失败')
			return

		# 用合并的记忆替换已处理的消息
		memory_message = HumanMessage(content=memory_content)
		memory_tokens = self.message_manager._count_tokens(memory_message)
		memory_metadata = MessageMetadata(tokens=memory_tokens, message_type='memory')

		# 计算被移除的总token数
		removed_tokens = sum(m.metadata.tokens for m in messages_to_process)

		# 添加记忆消息
		new_messages.append(ManagedMessage(message=memory_message, metadata=memory_metadata))

		# 更新历史记录
		self.message_manager.state.history.messages = new_messages
		self.message_manager.state.history.current_tokens -= removed_tokens
		self.message_manager.state.history.current_tokens += memory_tokens
		self.logger.info(f'📜 历史记录已合并: {len(messages_to_process)}个步骤转换为长期记忆')

	def _create(self, messages: list[BaseMessage], current_step: int) -> str | None:
		"""
		从给定的消息创建程序性记忆。

		Args:
		    messages: 要处理的消息列表
		    current_step: 当前步骤编号

		Returns:
		    创建的记忆内容，如果创建失败则返回None
		"""
		parsed_messages = convert_to_openai_messages(messages)
		try:
			results = self.mem0.add(
				messages=parsed_messages,
				agent_id=self.config.agent_id,
				memory_type='procedural_memory',
				metadata={'step': current_step},
			)
			if len(results.get('results', [])):
				return results.get('results', [])[0].get('memory')
			return None
		except Exception as e:
			self.logger.error(f'📜 创建程序性记忆时出错: {e}')
			return None
