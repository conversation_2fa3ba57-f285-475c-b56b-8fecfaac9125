import logging
import os
from pathlib import Path

from dotenv import load_dotenv
from posthog import Posthog
from uuid_extensions import uuid7str

load_dotenv()

from browser_use.config import CONFIG
from browser_use.telemetry.views import BaseTelemetryEvent
from browser_use.utils import singleton

logger = logging.getLogger(__name__)


POSTHOG_EVENT_SETTINGS = {
	'process_person_profile': True,
}


def xdg_cache_home() -> Path:
	default = Path.home() / '.cache'
	if CONFIG.XDG_CACHE_HOME and (path := Path(CONFIG.XDG_CACHE_HOME)).is_absolute():
		return path
	return default


@singleton
class ProductTelemetry:
	"""
	用于捕获匿名遥测数据的服务。

	如果环境变量 `ANONYMIZED_TELEMETRY=False`，匿名遥测将被禁用。
	"""

	USER_ID_PATH = str(xdg_cache_home() / 'browser_use' / 'telemetry_user_id')
	PROJECT_API_KEY = 'phc_F8JMNjW1i2KbGUTaW1unnDdLSPCoyc52SGRU0JecaUh'
	HOST = 'https://eu.i.posthog.com'
	UNKNOWN_USER_ID = 'UNKNOWN'

	_curr_user_id = None

	def __init__(self) -> None:
		telemetry_disabled = not CONFIG.ANONYMIZED_TELEMETRY
		self.debug_logging = CONFIG.BROWSER_USE_LOGGING_LEVEL == 'debug'

		if telemetry_disabled:
			self._posthog_client = None
		else:
			logger.info(
				'匿名遥测已启用。更多信息请参见 https://docs.browser-use.com/development/telemetry。'
			)
			self._posthog_client = Posthog(
				project_api_key=self.PROJECT_API_KEY,
				host=self.HOST,
				disable_geoip=False,
				enable_exception_autocapture=True,
			)

			# 静默posthog的日志记录
			if not self.debug_logging:
				posthog_logger = logging.getLogger('posthog')
				posthog_logger.disabled = True

		if self._posthog_client is None:
			logger.debug('遥测已禁用')

	def capture(self, event: BaseTelemetryEvent) -> None:
		if self._posthog_client is None:
			return

		self._direct_capture(event)

	def _direct_capture(self, event: BaseTelemetryEvent) -> None:
		"""
		不应该阻塞线程，因为posthog会神奇地处理它
		"""
		if self._posthog_client is None:
			return

		try:
			self._posthog_client.capture(
				self.user_id,
				event.name,
				{**event.properties, **POSTHOG_EVENT_SETTINGS},
			)
		except Exception as e:
			logger.error(f'发送遥测事件 {event.name} 失败: {e}')

	def flush(self) -> None:
		if self._posthog_client:
			try:
				self._posthog_client.flush()
				logger.debug('PostHog客户端遥测队列已刷新。')
			except Exception as e:
				logger.error(f'刷新PostHog客户端失败: {e}')
		else:
			logger.debug('PostHog客户端不可用，跳过刷新。')

	@property
	def user_id(self) -> str:
		if self._curr_user_id:
			return self._curr_user_id

		# 文件访问可能由于权限或其他原因失败。我们不希望
		# 崩溃，所以捕获所有异常。
		try:
			if not os.path.exists(self.USER_ID_PATH):
				os.makedirs(os.path.dirname(self.USER_ID_PATH), exist_ok=True)
				with open(self.USER_ID_PATH, 'w') as f:
					new_user_id = uuid7str()
					f.write(new_user_id)
				self._curr_user_id = new_user_id
			else:
				with open(self.USER_ID_PATH) as f:
					self._curr_user_id = f.read()
		except Exception:
			self._curr_user_id = 'UNKNOWN_USER_ID'
		return self._curr_user_id
