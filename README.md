<picture>
  <source media="(prefers-color-scheme: dark)" srcset="./static/browser-use-dark.png">
  <source media="(prefers-color-scheme: light)" srcset="./static/browser-use.png">
  <img alt="在浅色模式下显示黑色Browser Use Logo，在深色模式下显示白色Logo。" src="./static/browser-use.png"  width="full">
</picture>

<h1 align="center">让AI控制您的浏览器 🤖</h1>

[![GitHub stars](https://img.shields.io/github/stars/gregpr07/browser-use?style=social)](https://github.com/gregpr07/browser-use/stargazers)
[![Discord](https://img.shields.io/discord/1303749220842340412?color=7289DA&label=Discord&logo=discord&logoColor=white)](https://link.browser-use.com/discord)
[![Cloud](https://img.shields.io/badge/Cloud-☁️-blue)](https://cloud.browser-use.com)
[![Documentation](https://img.shields.io/badge/Documentation-📕-blue)](https://docs.browser-use.com)
[![Twitter Follow](https://img.shields.io/twitter/follow/Gregor?style=social)](https://x.com/intent/user?screen_name=gregpr07)
[![Twitter Follow](https://img.shields.io/twitter/follow/Magnus?style=social)](https://x.com/intent/user?screen_name=mamagnus00)
[![Weave Badge](https://img.shields.io/endpoint?url=https%3A%2F%2Fapp.workweave.ai%2Fapi%2Frepository%2Fbadge%2Forg_T5Pvn3UBswTHIsN1dWS3voPg%2F881458615&labelColor=#EC6341)](https://app.workweave.ai/reports/repository/org_T5Pvn3UBswTHIsN1dWS3voPg/881458615)

🌐 Browser-use 是连接AI代理与浏览器的最简单方式。

💡 在我们的 [Discord](https://link.browser-use.com/discord) 中查看其他人正在构建的项目并分享您的作品！想要周边商品？查看我们的 [商品店](https://browsermerch.com)。

🌤️ 跳过设置步骤 - 试用我们的<b>托管版本</b>，立即体验浏览器自动化！<b>[试用云服务 ☁︎](https://cloud.browser-use.com)</b>。

# 快速开始

使用 pip 安装 (Python>=3.11)：

```bash
pip install browser-use
```

安装记忆功能 (由于PyTorch兼容性要求Python<3.13)：

```bash
pip install "browser-use[memory]"
```

安装浏览器：
```bash
playwright install chromium --with-deps --no-shell
```

启动您的代理：

```python
import asyncio
from dotenv import load_dotenv
load_dotenv()
from browser_use import Agent
from langchain_openai import ChatOpenAI

async def main():
    agent = Agent(
        task="Compare the price of gpt-4o and DeepSeek-V3",
        llm=ChatOpenAI(model="gpt-4o"),
    )
    await agent.run()

asyncio.run(main())
```

将您要使用的提供商的API密钥添加到 `.env` 文件中：

```bash
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_KEY=
GOOGLE_API_KEY=
DEEPSEEK_API_KEY=
GROK_API_KEY=
NOVITA_API_KEY=
```

有关其他设置、模型等更多信息，请查看 [文档 📕](https://docs.browser-use.com)。

### 使用UI界面测试

您可以使用 [Web UI](https://github.com/browser-use/web-ui) 或 [桌面应用](https://github.com/browser-use/desktop) 来测试browser-use。

### 使用交互式CLI测试

您也可以使用我们的 `browser-use` 交互式CLI（类似于 `claude` 代码）：

```bash
pip install browser-use[cli]
browser-use
```

# 演示

<br/><br/>

[任务](https://github.com/browser-use/browser-use/blob/main/examples/use-cases/shopping.py)：将杂货商品添加到购物车并结账。

[![AI帮我买杂货](https://github.com/user-attachments/assets/a0ffd23d-9a11-4368-8893-b092703abc14)](https://www.youtube.com/watch?v=L2Ya9PYNns8)

<br/><br/>

提示词：将我最新的LinkedIn关注者添加到Salesforce的潜在客户中。

![LinkedIn到Salesforce](https://github.com/user-attachments/assets/50d6e691-b66b-4077-a46c-49e9d4707e07)

<br/><br/>

[提示词](https://github.com/browser-use/browser-use/blob/main/examples/use-cases/find_and_apply_to_jobs.py)：阅读我的简历并找到机器学习工作，将它们保存到文件中，然后在新标签页中开始申请，如果需要帮助，请询问我。

https://github.com/user-attachments/assets/171fb4d6-0355-46f2-863e-edb04a828d04

<br/><br/>

[提示词](https://github.com/browser-use/browser-use/blob/main/examples/browser/real_browser.py)：在Google Docs中给我爸爸写一封信，感谢他的一切，并将文档保存为PDF。

![给爸爸的信](https://github.com/user-attachments/assets/242ade3e-15bc-41c2-988f-cbc5415a66aa)

<br/><br/>

[提示词](https://github.com/browser-use/browser-use/blob/main/examples/custom-functions/save_to_file_hugging_face.py)：在Hugging Face上查找许可证为cc-by-sa-4.0的模型，按点赞数排序，将前5个保存到文件中。

https://github.com/user-attachments/assets/de73ee39-432c-4b97-b4e8-939fd7f323b3

<br/><br/>

## 更多示例

更多示例请查看 [examples](examples) 文件夹，或加入 [Discord](https://link.browser-use.com/discord) 展示您的项目。您也可以查看我们的 [`awesome-prompts`](https://github.com/browser-use/awesome-prompts) 仓库获取提示词灵感。

# 愿景

告诉您的计算机要做什么，它就会完成。

## 路线图

### 代理 (Agent)

- [ ] 改进代理记忆以处理100+步骤
- [ ] 增强规划能力（加载网站特定上下文）
- [ ] 减少token消耗（系统提示词、DOM状态）

### DOM提取

- [ ] 启用所有可能UI元素的检测
- [ ] 改进UI元素的状态表示，使所有LLM都能理解页面内容

### 工作流

- [ ] 让用户记录工作流 - 我们可以使用browser-use作为后备重新运行
- [ ] 使工作流重新运行正常工作，即使页面发生变化

### 用户体验

- [ ] 为教程执行、求职申请、QA测试、社交媒体等创建各种模板，用户可以直接复制粘贴使用
- [ ] 改进文档
- [ ] 提高速度

### 并行化

- [ ] 人类工作是顺序的。浏览器代理的真正威力在于我们可以并行化类似任务。例如，如果您想找到100家公司的联系信息，这些都可以并行完成并报告给主代理，主代理处理结果并再次启动并行子任务。


## 贡献

我们欢迎贡献！请随时为错误或功能请求开启issues。要为文档做贡献，请查看 `/docs` 文件夹。


## 🧪 如何让您的代理更加稳健？

我们提供在CI中运行您的任务——每次更新时自动运行！

- **添加您的任务：** 在 `tests/agent_tasks/` 中添加YAML文件（详情请参见[那里的README](tests/agent_tasks/README.md)）。
- **自动验证：** 每次我们推送更新时，您的任务将由代理运行并使用您的标准进行评估。

## 本地设置

要了解更多关于该库的信息，请查看 [本地设置 📕](https://docs.browser-use.com/development/local-setup)。

`main` 是主要的开发分支，变化频繁。对于生产使用，请安装稳定的 [版本发布](https://github.com/browser-use/browser-use/releases)。

---

## 周边商品

想要展示您的Browser-use周边商品？查看我们的 [商品店](https://browsermerch.com)。优秀的贡献者将免费获得周边商品 👀。

## 引用

如果您在研究或项目中使用Browser Use，请引用：

```bibtex
@software{browser_use2024,
  author = {Müller, Magnus and Žunič, Gregor},
  title = {Browser Use: Enable AI to control your browser},
  year = {2024},
  publisher = {GitHub},
  url = {https://github.com/browser-use/browser-use}
}
```

 <div align="center"> <img src="https://github.com/user-attachments/assets/06fa3078-8461-4560-b434-445510c1766f" width="400"/>

[![Twitter Follow](https://img.shields.io/twitter/follow/Gregor?style=social)](https://x.com/intent/user?screen_name=gregpr07)
[![Twitter Follow](https://img.shields.io/twitter/follow/Magnus?style=social)](https://x.com/intent/user?screen_name=mamagnus00)

 </div>

<div align="center">
在苏黎世和旧金山用 ❤️ 制作
 </div>
